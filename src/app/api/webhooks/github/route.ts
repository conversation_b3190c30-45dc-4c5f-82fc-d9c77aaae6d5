import { NextRequest, NextResponse } from 'next/server'
import { verifyGitHubWebhook, isEventFromPlatyfendApp } from '@/src/lib/github/app-auth'
import {
  handleInstallationWebhook,
  handleInstallationRepositoriesWebhook,
  handleRepositoryWebhook,
  handlePullRequestWebhook
} from '@/src/lib/services/webhook-sync'

export async function POST(request: NextRequest) {
    try {
        // Get webhook payload and signature
        const body = await request.text()
        const signature = request.headers.get('x-hub-signature-256') || ''
        const event = request.headers.get('x-github-event') || ''

        // Verify webhook signature
        if (!verifyGitHubWebhook(body, signature)) {
            console.error('Invalid webhook signature')
            return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
        }

        const payload = JSON.parse(body)

        // ✅ Immediately ACK GitHub to stop retries for pull request open/reopen events
        if (event === 'pull_request' && (payload.action === 'opened' || payload.action === 'reopened')) {
            console.log(`Pull request ${payload.action} - sending immediate ACK to GitHub`)

            // Process webhook asynchronously to avoid blocking GitHub
            setImmediate(async () => {
                try {
                    console.log(`Processing ${event} asynchronously:`, {
                        action: payload.action,
                        installationId: payload.installation?.id,
                        senderId: payload.sender?.id,
                        senderLogin: payload.sender?.login,
                        prNumber: payload.pull_request?.number,
                        repository: payload.repository?.full_name
                    })

                    // Prevent infinite loops: Ignore events triggered by the app itself
                    if (isEventFromPlatyfendApp(payload.sender)) {
                        console.log(`Ignoring async webhook event triggered by Platyfend app: ${event} (sender: ${payload.sender?.login})`)
                        return
                    }

                    const syncResult = await handlePullRequestWebhook(payload)

                    if (syncResult && !syncResult.success) {
                        console.error(`Async webhook sync failed for ${event}:`, syncResult.errors)
                    } else if (syncResult) {
                        console.log(`Async webhook sync completed for ${event}:`, {
                            action: syncResult.action,
                            repositoriesAffected: syncResult.repositoriesAffected,
                            organizationId: syncResult.organizationId
                        })
                    }
                } catch (asyncError) {
                    console.error('Error in async webhook processing:', asyncError)
                }
            })

            return new NextResponse("OK", { status: 200 })
        }

        console.log(`GitHub webhook received: ${event}`, {
            action: payload.action,
            installationId: payload.installation?.id,
            senderId: payload.sender?.id,
            senderLogin: payload.sender?.login,
            repositorySelection: payload.repository_selection,
            repositoriesCount: payload.repositories?.length || 0
        })

        // Prevent infinite loops: Ignore events triggered by the app itself
        if (isEventFromPlatyfendApp(payload.sender)) {
            console.log(`Ignoring webhook event triggered by Platyfend app: ${event} (sender: ${payload.sender?.login})`)
            return NextResponse.json({ success: true, ignored: true, reason: 'app_triggered' })
        }

        // Handle other webhook events synchronously
        let syncResult;
        switch (event) {
            case 'installation':
                syncResult = await handleInstallationWebhook(payload)
                break
            case 'installation_repositories':
                syncResult = await handleInstallationRepositoriesWebhook(payload)
                break
            case 'repository':
                syncResult = await handleRepositoryWebhook(payload)
                break
            case 'pull_request':
                // Handle other PR actions synchronously (closed, etc.)
                syncResult = await handlePullRequestWebhook(payload)
                break
            case 'issue_comment':
            case 'pull_request_review_comment':
            case 'pull_request_review':
                // Ignore comment events to prevent infinite loops
                console.log(`Ignoring comment event: ${event} (action: ${payload.action})`)
                syncResult = { success: true, action: `ignored_${event}`, repositoriesAffected: 0, errors: [] }
                break
            default:
                console.log(`Unhandled webhook event: ${event}`)
                syncResult = { success: true, action: `unhandled_${event}`, repositoriesAffected: 0, errors: [] }
        }

        // Log sync result
        if (syncResult && !syncResult.success) {
            console.error(`Webhook sync failed for ${event}:`, syncResult.errors)
        } else if (syncResult) {
            console.log(`Webhook sync completed for ${event}:`, {
                action: syncResult.action,
                repositoriesAffected: syncResult.repositoriesAffected,
                organizationId: syncResult.organizationId
            })
        }

        return NextResponse.json({ success: true })

    } catch (error) {
        console.error('GitHub webhook error:', error)
        return NextResponse.json({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
    }
}






